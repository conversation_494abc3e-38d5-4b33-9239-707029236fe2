import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:http/http.dart' as http;
import 'package:http/http.dart' as Http;
import 'package:wzzff/core/utils/app_messages.dart';
import 'package:wzzff/main.dart';
import 'package:wzzff/models/Encryption.dart';
import 'package:wzzff/core/constants/Constants.dart';

class LoginAndCheckAndRegi {
    Future seeker_login(email, password, BuildContext context, {String? fcmToken}) async {
    final storage = FlutterSecureStorage();
    String url = "${Constants.url}/loginSeeker";
    Uri urlConvert = Uri.parse(url);
    Map<String, dynamic> loginData = {"email": email, "password": password};
    if (fcmToken != null) {
      loginData["fcm_token"] = fcmToken;
    }
    //print(loginData);
    Map<String, String> headers = {
      "Content-Type": "application/json",
      "Accept": "application/json",
      "Authorization": "Bearer ${Encryption.instance
              .encrypt(jsonEncode(loginData))}"
    };
    http.Response response = await Http.post(urlConvert, headers: headers);

    String decryptData = Encryption.instance.decrypt(
      response.body
    );
   
    Map<String, dynamic> data = jsonDecode(decryptData);
    if (data["status"] == 0) {
      AppMessages.showError(data["message"]);
    } else if (data["status"] == 1) {
      final storage = FlutterSecureStorage();
      await storage.write(key: "user_name", value: data["user"]["name"]);
      await storage.write(key: "api_token", value: data["user"]["api_token"]);
      await storage.write(key: "user_id", value: data["user"]["id"].toString());
      await storage.write(key: "email", value: data["user"]["email"]);

      AppMessages.showSuccess("تم تسجيل الدخول بنجاح");

      // تعديل: الانتقال إلى الشاشة الرئيسية مع تحديد تاب الملف الشخصي
      Navigator.pushAndRemoveUntil(
        context,
        MaterialPageRoute(builder: (context) => MysideHaveHome(title: Constants.appName, initialTabIndex: 2)),
        (route) => false
      );
    }
  }

  // تسجيل الدخول
  static Future<Map<String, dynamic>?> login({
    required String email,
    required String password,
    String? fcmToken,
  }) async {
    try {
      final String url = "${Constants.url}/login";
      final Uri urlConvert = Uri.parse(url);

      final Map<String, dynamic> loginData = {
        'email': email,
        'password': password,
        if (fcmToken != null) 'fcm_token': fcmToken,
      };

      final Map<String, String> headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'encrypt': "${Encryption.instance.encrypt(jsonEncode(loginData))}"
      };

      http.Response response = await http.post(urlConvert, headers: headers);

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);
        return responseData;
      } else {
        return null;
      }
    } catch (e) {
      return null;
    }
  }

  // التحقق من المستخدم
  static Future<Map<String, dynamic>?> checkUser({
    required String token,
  }) async {
    try {
      final String url = "${Constants.url}/check-user";
      final Uri urlConvert = Uri.parse(url);

      final Map<String, String> headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': 'Bearer $token',
      };

      http.Response response = await http.get(urlConvert, headers: headers);

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);
        return responseData;
      } else {
        return null;
      }
    } catch (e) {
      return null;
    }
  }

  // التسجيل
  static Future<Map<String, dynamic>?> register({
    required String name,
    required String email,
    required String password,
    required String phone,
    required String userType,
    String? fcmToken,
  }) async {
    try {
      final String url = "${Constants.url}/register";
      final Uri urlConvert = Uri.parse(url);

      final Map<String, dynamic> registerData = {
        'name': name,
        'email': email,
        'password': password,
        'phone': phone,
        'user_type': userType,
        if (fcmToken != null) 'fcm_token': fcmToken,
      };

      final Map<String, String> headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'encrypt': "${Encryption.instance.encrypt(jsonEncode(registerData))}"
      };

      http.Response response = await http.post(urlConvert, headers: headers);

      if (response.statusCode == 200 || response.statusCode == 201) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);
        return responseData;
      } else {
        return null;
      }
    } catch (e) {
      return null;
    }
  }

  // نسيان كلمة المرور
  static Future<Map<String, dynamic>?> forgotPassword({
    required String email,
  }) async {
    try {
      final String url = "${Constants.url}/forgot-password";
      final Uri urlConvert = Uri.parse(url);

      final Map<String, dynamic> data = {
        'email': email,
      };

      final Map<String, String> headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'encrypt': "${Encryption.instance.encrypt(jsonEncode(data))}"
      };

      http.Response response = await http.post(urlConvert, headers: headers);

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);
        return responseData;
      } else {
        return null;
      }
    } catch (e) {
      return null;
    }
  }

  // تسجيل الخروج
  static Future<bool> logout({
    required String token,
  }) async {
    try {
      final String url = "${Constants.url}/logout";
      final Uri urlConvert = Uri.parse(url);

      final Map<String, String> headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': 'Bearer $token',
      };

      http.Response response = await http.post(urlConvert, headers: headers);

      return response.statusCode == 200;
    } catch (e) {
      return false;
    }
  }

  // رفع السيرة الذاتية
  static Future<bool> uploadCv(File cvFile) async {
    try {
      final String url = "${Constants.url}/upload-cv";
      final Uri urlConvert = Uri.parse(url);

      var request = http.MultipartRequest('POST', urlConvert);
      request.files.add(
        await http.MultipartFile.fromPath('cv_file', cvFile.path)
      );

      var response = await request.send();
      return response.statusCode == 200;
    } catch (e) {
      return false;
    }
  }
}
