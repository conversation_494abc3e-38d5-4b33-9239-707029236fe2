import 'package:flutter/material.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../../Apis/CompanyApi.dart';
import '../../Company/Company/company_dashboard.dart';
// إضافة استيراد صفحة داشبورد الباحث عن عمل عند الحاجة

class UserService {
  static const _storage = FlutterSecureStorage();

  /// الحصول على نوع المستخدم الحالي
  static Future<String?> getCurrentUserType() async {
    return await CompanyApi.getCurrentUserType();
  }

  /// الحصول على الـ token المناسب حسب نوع المستخدم
  static Future<String?> getCurrentUserToken() async {
    return await CompanyApi.getCurrentUserToken();
  }

  /// التحقق من وجود مستخدم مسجل دخول
  static Future<bool> isUserLoggedIn() async {
    final userType = await getCurrentUserType();
    return userType != null;
  }

  /// توجيه المستخدم إلى الداشبورد المناسب
  static Future<void> navigateToUserDashboard(BuildContext context) async {
    final userType = await getCurrentUserType();
    
    switch (userType) {
      case 'company':
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(builder: (context) => CompanyDashboard()),
        );
        break;
      case 'seeker':
        // توجيه إلى داشبورد الباحث عن عمل
        // Navigator.pushReplacement(
        //   context,
        //   MaterialPageRoute(builder: (context) => SeekerDashboard()),
        // );
        Navigator.pushReplacementNamed(context, '/seeker_dashboard');
        break;
      default:
        // إذا لم يكن هناك مستخدم مسجل دخول، توجيه لصفحة تسجيل الدخول
        Navigator.pushReplacementNamed(context, '/login');
        break;
    }
  }

  /// تسجيل خروج المستخدم الحالي
  static Future<void> logoutCurrentUser() async {
    final userType = await getCurrentUserType();
    
    switch (userType) {
      case 'company':
        await CompanyApi().logoutCompany();
        break;
      case 'seeker':
        // تسجيل خروج الباحث عن عمل
        await _storage.delete(key: 'api_token');
        break;
    }
  }

  /// الحصول على اسم المستخدم الحالي
  static Future<String?> getCurrentUserName() async {
    final userType = await getCurrentUserType();
    
    switch (userType) {
      case 'company':
        return await _storage.read(key: 'company_name');
      case 'seeker':
        // الحصول على اسم الباحث عن عمل
        // يمكن إضافة منطق للحصول على اسم الباحث
        return await _storage.read(key: 'seeker_name');
      default:
        return null;
    }
  }

  /// الحصول على بريد المستخدم الحالي
  static Future<String?> getCurrentUserEmail() async {
    final userType = await getCurrentUserType();
    
    switch (userType) {
      case 'company':
        return await _storage.read(key: 'company_email');
      case 'seeker':
        // الحصول على بريد الباحث عن عمل
        return await _storage.read(key: 'seeker_email');
      default:
        return null;
    }
  }

  /// التحقق من صلاحية المستخدم للوصول لصفحات الشركة
  static Future<bool> canAccessCompanyPages() async {
    final userType = await getCurrentUserType();
    return userType == 'company';
  }

  /// التحقق من صلاحية المستخدم للوصول لصفحات الباحث عن عمل
  static Future<bool> canAccessSeekerPages() async {
    final userType = await getCurrentUserType();
    return userType == 'seeker';
  }
} 