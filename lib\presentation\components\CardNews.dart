import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:wzzff/models/NewsModel.dart';
// import '../screens/articles/ArtcalPage.dart'; // معطل مؤقتاً

class CardNews extends StatelessWidget {
  final NewsModel data;

  const CardNews({super.key, required this.data});

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: GestureDetector(
        onTap: () {
          // معطل مؤقتاً - يحتاج إصلاح مسار ArtcalPage
          // Navigator.push(context, MaterialPageRoute(builder: (context) {
          //   return ArtcalPage(data: data);
          // }));
        },
        child: Container(
          decoration: BoxDecoration(
            color: Theme.of(context).brightness == Brightness.dark
                ? Theme.of(context).cardTheme.color
                : Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.black.withOpacity(0.2)
                    : Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          clipBehavior: Clip.antiAliasWithSaveLayer,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // صورة المقال
              _buildArticleImage(context),

              // محتوى المقال
              _buildArticleContent(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildArticleImage(BuildContext context) {
    return Stack(
      children: [
        Hero(
          tag: 'article_image_${data.id}',
          child: CachedNetworkImage(
            height: 160,
            width: double.infinity,
            fit: BoxFit.cover,
            imageUrl: "https://wzzff.com/${Uri.encodeFull(data.image)}",
            progressIndicatorBuilder: (context, url, downloadProgress) => Container(
              height: 160,
              color: Theme.of(context).brightness == Brightness.dark
                  ? Theme.of(context).colorScheme.surface
                  : Colors.grey[200],
              child: Center(
                child: CircularProgressIndicator(
                  color: Theme.of(context).colorScheme.primary,
                  value: downloadProgress.progress,
                ),
              ),
            ),
            errorWidget: (context, url, error) => Container(
              height: 160,
              color: Theme.of(context).brightness == Brightness.dark
                  ? Theme.of(context).colorScheme.surface
                  : Colors.grey[200],
              child: Center(
                child: Icon(
                  Icons.error,
                  color: Theme.of(context).brightness == Brightness.dark
                      ? Colors.grey[500]
                      : Colors.grey[400],
                  size: 32
                ),
              ),
            ),
          ),
        ),
        // شريط التصنيف
        Positioned(
          top: 12,
          right: 12,
          child: _buildCategoryBadge(),
        ),
        // شريط التاريخ
        Positioned(
          bottom: 0,
          left: 0,
          right: 0,
          child: _buildDateBar(),
        ),
      ],
    );
  }

  Widget _buildCategoryBadge() {
    return Builder(
      builder: (context) {
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primary,
            borderRadius: BorderRadius.circular(30),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(
                Icons.article_outlined,
                color: Colors.white,
                size: 14,
              ),
              const SizedBox(width: 4),
              Text(
                "مقالات",
                style: GoogleFonts.tajawal(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildDateBar() {
    return Builder(
      builder: (context) {
        return Container(
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Colors.transparent,
                Colors.black.withOpacity(0.7),
              ],
            ),
          ),
          child: Row(
            children: [
              const Icon(
                Icons.access_time,
                color: Colors.white,
                size: 14,
              ),
              const SizedBox(width: 6),
              Text(
                data.time,
                style: GoogleFonts.tajawal(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildArticleContent(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان المقال
          Text(
            data.title,
            style: GoogleFonts.tajawal(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).brightness == Brightness.dark
                  ? Theme.of(context).textTheme.titleLarge?.color
                  : const Color(0xFF333333),
              height: 1.4,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),

          const SizedBox(height: 12),

          // معلومات المصدر وزر القراءة
          Row(
            children: [
              // معلومات المصدر
              _buildSourceInfo(context),

              const Spacer(),

              // زر القراءة
              _buildReadMoreButton(context),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSourceInfo(BuildContext context) {
    return Row(
      children: [
        Container(
          height: 24,
          width: 24,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
            border: Border.all(
              color: Theme.of(context).colorScheme.primary.withOpacity(0.2),
              width: 1,
            ),
          ),
          child: const CircleAvatar(
            backgroundImage: AssetImage("assets/company.png"),
          ),
        ),
        const SizedBox(width: 8),
        Text(
          "وظف دوت كوم",
          style: GoogleFonts.tajawal(
            fontSize: 13,
            fontWeight: FontWeight.w500,
            color: Theme.of(context).brightness == Brightness.dark
                ? Theme.of(context).textTheme.bodyMedium?.color
                : Colors.grey[700],
          ),
        ),
      ],
    );
  }

  Widget _buildReadMoreButton(BuildContext context) {
    final primaryColor = Theme.of(context).colorScheme.primary;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: primaryColor.withOpacity(
            Theme.of(context).brightness == Brightness.dark ? 0.2 : 0.1),
        borderRadius: BorderRadius.circular(30),
        border: Border.all(
          color: primaryColor.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.remove_red_eye_outlined,
            size: 14,
            color: primaryColor,
          ),
          const SizedBox(width: 6),
          Text(
            " المزيد",
            style: GoogleFonts.tajawal(
              fontSize: 13,
              color: primaryColor,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}
