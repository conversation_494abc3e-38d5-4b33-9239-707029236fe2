import 'dart:math';

import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:wzzff/Apis/LoginAndCheckAndRegi.dart';
import 'package:wzzff/presentation/screens/job_seeker/AppliedJobsScreen.dart';
import 'package:wzzff/presentation/screens/job_seeker/FivSeekerJobs.dart';
import 'package:wzzff/presentation/screens/auth/LoginOrRegisterScreen.dart';
import 'package:wzzff/presentation/screens/job_seeker/ProfileSeekerInformations.dart';
import 'package:wzzff/presentation/screens/job_seeker/SavedJobs.dart';
import 'package:wzzff/presentation/screens/job_seeker/SmartMatchedJobsScreen.dart';
import 'package:wzzff/presentation/screens/job_seeker/UploadSeekerCv.dart';
import '../../../core/utils/app_messages.dart';
import '../../../main.dart';
import 'package:provider/provider.dart';
import 'package:wzzff/core/providers/app_state_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:intl/intl.dart';
import 'dart:convert';
import 'dart:io';
import 'package:wzzff/models/SeekerModel.dart';
import 'package:wzzff/presentation/screens/create_cv/cv_creator_main.dart';

class SeekerScreenProfile extends StatefulWidget {
  const SeekerScreenProfile({super.key});

  @override
  State<SeekerScreenProfile> createState() => _SeekerScreenProfileState();
}

class _SeekerScreenProfileState extends State<SeekerScreenProfile> {
  
  // دالة للتحقق من وجود توكن المستخدم
  Future<bool> _checkUserToken() async {
    try {
      final storage = FlutterSecureStorage();
      final token = await storage.read(key: 'api_token');
      if (token != null && token.isNotEmpty) {
        // التحقق من صحة التوكن مع الخادم
        final result = await LoginAndCheckAndRegi.checkUser(token: token);
        return result != null;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  // دالة تسجيل الخروج
  Future<void> _logout() async {
    try {
      const storage = FlutterSecureStorage();
      final token = await storage.read(key: 'api_token');
      if (token != null) {
        await LoginAndCheckAndRegi.logout(token: token);
      }
      // حذف جميع البيانات المحفوظة
      await storage.delete(key: 'api_token');
      await storage.delete(key: 'user_name');
      await storage.delete(key: 'user_id');
      await storage.delete(key: 'email');
    } catch (e) {
      // في حالة الخطأ، حذف جميع البيانات
      const storage = FlutterSecureStorage();
      await storage.deleteAll();
    }
  }

  // دالة حذف الحساب
  Future<bool> _deleteAccount() async {
    try {
      const storage = FlutterSecureStorage();
      final token = await storage.read(key: 'api_token');
      if (token != null) {
        // استدعاء API حذف الحساب
        final success = await LoginAndCheckAndRegi.logout(token: token);
        // حذف جميع البيانات المحفوظة
        await storage.deleteAll();
        return success;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        child: FutureBuilder(
          future: _checkUserToken(),
          builder: (context, AsyncSnapshot<bool> snapshot) {
            if (snapshot.hasError) {
              Provider.of<AppStateProvider>(context, listen: false).setServerError(
                'حدث خطأ أثناء التحقق من المستخدم. سيتم إعادة المحاولة تلقائيًا.',
                () => setState(() {}),
              );
              return const SizedBox.shrink();
            }
            if (snapshot.hasData) {
              if (snapshot.data! == true) {
                return Column(
                  children: [
                   // _buildWaveHeader(),
                    Expanded(
                      child: _buildProfileContent(),
                    ),
                  ],
                );
              } else {
                return LoginOrRegisterScreen();
              }
            } else {
              return const Center(
                child: CircularProgressIndicator(
                  color: Color(0xFF2daae2),
                ),
              );
            }
          },
        ),
      ),
    );
  }


  // إضافة WaveClipper لرسم الموجة
  Widget _buildProfileContent() {
    return Column(
      children: [
        Expanded(
          child: Container(
            decoration: BoxDecoration(
              color: Theme.of(context).brightness == Brightness.dark
                  ? Theme.of(context).scaffoldBackgroundColor
                  : Colors.white,
              borderRadius: const BorderRadius.only(
               // topLeft: Radius.circular(30),
               // topRight: Radius.circular(30),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(Theme.of(context).brightness == Brightness.dark ? 0.2 : 0.05),
                  blurRadius: 10,
                  offset: const Offset(0, -5),
                ),
              ],
            ),
            child: ListView(
              padding: const EdgeInsets.only(top: 20, bottom: 20),
              children: [
                SizedBox(height: 20),
                
                _buildProfileSection("إدارة الحساب", [
                  _buildProfileMenuItem(
                    "معلومات شخصية",
                    Icons.account_circle,
                    const Color(0xFF2daae2),
                    () {
                      Navigator.push(context,
                          MaterialPageRoute(builder: ((context) {
                        return ProfileSeekerInformations();
                      })));
                    },
                  ),
                  _buildProfileMenuItem(
                    "السيرة الذاتية",
                    Icons.picture_as_pdf,
                    const Color(0xFF4CAF50),
                    () {
                      Navigator.push(context,
                          MaterialPageRoute(builder: ((context) {
                        return UploadSeekerCv();
                      })));
                    },
                  ),
                ]),

                const SizedBox(height: 20),

                _buildProfileSection("الوظائف", [
                
                  _buildProfileMenuItem(
                    "الوظائف المفضلة",
                    Icons.favorite,
                    const Color(0xFFE91E63),
                    () {
                      Navigator.push(context,
                          MaterialPageRoute(builder: ((context) {
                        return SavedJobs();
                      })));
                    },
                  ),
                  _buildProfileMenuItem(
                    "الوظائف المتقدم لها",
                    Icons.work_outline,
                    const Color(0xFF9C27B0),
                    () {
                      Navigator.push(context,
                          MaterialPageRoute(builder: ((context) {
                        return AppliedJobsScreen();
                      })));
                    },
                  ),
                ]),

                const SizedBox(height: 20),

                _buildProfileSection("الإعدادات", [
                  _buildProfileMenuItem(
                    "الإشعارات",
                    Icons.notifications_none,
                    const Color(0xFF3F51B5),
                    () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => 
                            const NotificationsScreen(),
                        ),
                      );
                    },
                  ),
                
                  _buildProfileMenuItem(
                    "تسجيل خروج",
                    Icons.logout_outlined,
                    const Color(0xFFf44336),
                    () {
                      AppMessages.showConfirmation(
                        context: context,
                        message: "هل أنت متأكد من تسجيل الخروج؟",
                        confirmText: "تسجيل خروج",
                        onConfirm: () async {
                          await _logout();
                          AppMessages.showInfo("تم تسجيل الخروج بنجاح");
                          Navigator.pushAndRemoveUntil(
                            context,
                            MaterialPageRoute(
                              builder: (context) => MysideHaveHome(title: "Wzzff", initialTabIndex: 0),
                            ),
                            (route) => false,
                          );
                        },
                      );
                    },
                  ),
                  _buildProfileMenuItem(
                    "حذف الحساب",
                    Icons.delete_forever,
                    Colors.red,
                    () {
                      AppMessages.showConfirmation(
                        context: context,
                        message: "تنبيه: حذف الحساب إجراء نهائي ولا يمكن التراجع عنه. سيتم حذف جميع بياناتك ولن تتمكن من استعادتها لاحقًا.\n\nهل أنت متأكد أنك تريد حذف حسابك؟",
                        confirmText: "حذف الحساب نهائيًا",
                        onConfirm: () async {
                          final success = await _deleteAccount();
                          if (success) {
                            AppMessages.showInfo("تم حذف الحساب بنجاح. ناسف علي اي تجربة سيئه .");
                            Navigator.pushAndRemoveUntil(
                              context,
                              MaterialPageRoute(
                                builder: (context) => MysideHaveHome(title: "Wzzff", initialTabIndex: 0),
                              ),
                              (route) => false,
                            );
                          } else {
                            AppMessages.showError("فشل حذف الحساب. حاول مرة أخرى أو تواصل مع الدعم.");
                          }
                        },
                      );
                    },
                  ),
                ]),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildProfileSection(String title, List<Widget> items) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Text(
            title,
            style: GoogleFonts.tajawal(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: isDarkMode ? Theme.of(context).colorScheme.primary : const Color(0xFF043955),
            ),
          ),
        ),
        const SizedBox(height: 10),
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 15),
          decoration: BoxDecoration(
            color: isDarkMode ? Theme.of(context).cardTheme.color : Colors.white,
            borderRadius: BorderRadius.circular(15),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(isDarkMode ? 0.2 : 0.05),
                blurRadius: 10,
                spreadRadius: 0,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: items,
          ),
        ),
      ],
    );
  }

  Widget _buildProfileMenuItem(
      String text, IconData icon, Color iconColor, VoidCallback onTap) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 15),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: Colors.grey.withOpacity(isDarkMode ? 0.25 : 0.15),
              width: 1,
            ),
          ),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: iconColor.withOpacity(isDarkMode ? 0.2 : 0.1),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Icon(
                icon,
                color: iconColor,
                size: 22,
              ),
            ),
            const SizedBox(width: 15),
            Expanded(
              child: Text(
                text,
                style: GoogleFonts.tajawal(
                  fontSize: 16,
                  color: isDarkMode ? Theme.of(context).textTheme.bodyLarge?.color : const Color(0xFF333333),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: isDarkMode ? Colors.grey[400] : Colors.grey,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }



  Widget _buildStatItem(String title, String value) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return Column(
      children: [
        Text(
          value,
          style: GoogleFonts.tajawal(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: isDarkMode ? Colors.white : const Color(0xFF2E7D32),
          ),
        ),
        Text(
          title,
          style: GoogleFonts.tajawal(
            fontSize: 10,
            color: isDarkMode ? Colors.grey[300] : const Color(0xFF388E3C),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

}

class WaveClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    var path = Path();
    path.lineTo(0, size.height);
    path.lineTo(size.width, size.height);
    path.lineTo(size.width, 0);

    // تحسين شكل الموجة لتكون أكثر انسيابية وجمالية
    var firstControlPoint = Offset(size.width * 0.75, size.height - 30);
    var firstEndPoint = Offset(size.width * 0.5, size.height - 20);
    path.quadraticBezierTo(
      firstControlPoint.dx, firstControlPoint.dy,
      firstEndPoint.dx, firstEndPoint.dy
    );

    var secondControlPoint = Offset(size.width * 0.25, size.height - 10);
    var secondEndPoint = Offset(0, size.height - 30);
    path.quadraticBezierTo(
      secondControlPoint.dx, secondControlPoint.dy,
      secondEndPoint.dx, secondEndPoint.dy
    );

    path.close();
    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) => false;
}

// صفحة الإشعارات البسيطة
class NotificationsScreen extends StatelessWidget {
  const NotificationsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'الإشعارات',
          style: GoogleFonts.tajawal(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Theme.of(context).colorScheme.primary,
        centerTitle: true,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.notifications_outlined,
              size: 80,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 20),
            Text(
              'لا توجد إشعارات حالياً',
              style: GoogleFonts.tajawal(
                fontSize: 18,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
